{
    "name": "sglang",
    "build": {
        "dockerfile": "Dockerfile"
    },
    "remoteUser": "devuser",
    "customizations": {
        "vscode": {
            "extensions": [
                // Python development
                "ms-python.python",
                "charliermarsh.ruff",
                // Rust development
                "rust-lang.rust-analyzer",
                "tamasfe.even-better-toml"
            ]
        }
    },
    "forwardPorts": [],
    "runArgs": [
        "--gpus",
        "all"
    ],
    // The two lines below ensures that your local changes in the sglang
    // repo is automatically synced to the sglang pip package installed
    // in the dev docker container. You can remove / comment out these
    // two lines if you prefer to sync code changes manually.
    "workspaceMount": "source=${localWorkspaceFolder},target=/sgl-workspace/sglang,type=bind",
    "workspaceFolder": "/sgl-workspace/sglang"
}
