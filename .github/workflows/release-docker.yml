name: Release Docker Images
on:
  push:
    branches:
      - main
    paths:
      - "python/sglang/version.py"
  workflow_dispatch:

jobs:
  publish:
    if: github.repository == 'sgl-project/sglang'
    runs-on: ubuntu-latest
    environment: 'prod'
    strategy:
      matrix:
        cuda_version: ['12.4.1']
        build_type: ['all']
    steps:
      - name: Delete huge unnecessary tools folder
        run: rm -rf /opt/hostedtoolcache

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Login to <PERSON>er Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and Push
        run: |
          version=$(cat python/sglang/version.py | cut -d'"' -f2)

          if [ "${{ matrix.cuda_version }}" = "11.8.0" ]; then
            cuda_tag="cu118"
          elif [ "${{ matrix.cuda_version }}" = "12.1.1" ]; then
            cuda_tag="cu121"
          elif [ "${{ matrix.cuda_version }}" = "12.4.1" ]; then
            cuda_tag="cu124"
          elif [ "${{ matrix.cuda_version }}" = "12.5.1" ]; then
            cuda_tag="cu125"
          else
            echo "Unsupported CUDA version"
            exit 1
          fi

          tag=v${version}-${cuda_tag}

          if [ "${{ matrix.build_type }}" = "all" ]; then
            tag_suffix=""
          elif [ "${{ matrix.build_type }}" = "srt" ]; then
            tag_suffix="-srt"
          else
            echo "Unsupported build type"
            exit 1
          fi

          docker build . -f docker/Dockerfile --build-arg CUDA_VERSION=${{ matrix.cuda_version }} --build-arg BUILD_TYPE=${{ matrix.build_type }} -t lmsysorg/sglang:${tag}${tag_suffix} --no-cache
          docker push lmsysorg/sglang:${tag}${tag_suffix}

          if [ "${{ matrix.cuda_version }}" = "12.4.1" ]; then
            docker tag lmsysorg/sglang:${tag}${tag_suffix} lmsysorg/sglang:latest${tag_suffix}
            docker push lmsysorg/sglang:latest${tag_suffix}
          fi
