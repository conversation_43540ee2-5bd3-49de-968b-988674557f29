name: Release Documentation

on:
  push:
    branches:
      - main
    paths:
      - 'docs/**'
      - 'python/sglang/version.py'
      - "python/sglang/**"
  workflow_dispatch:

concurrency:
  group: release-docs-${{ github.ref }}
  cancel-in-progress: true

jobs:
  execute-and-deploy:
    runs-on: 1-gpu-runner
    if: github.repository == 'sgl-project/sglang'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          find /public_sglang_ci/runner-a-gpu-1/_work/_tool/Python/3.9.21/x64/lib/python3.9/site-packages -name "sgl-kernel*" -exec rm -rf {} + || true
          bash scripts/ci_install_dependency.sh
          pip install -r docs/requirements.txt
          apt-get update
          apt-get install -y pandoc
          apt-get update && apt-get install -y parallel retry

          ln -sf "$(which python3)" /usr/bin/python

      - name: Setup Jupyter Kernel
        run: |
          python -m ipykernel install --user --name python3 --display-name "Python 3"

      - name: Execute notebooks and push to documents
        env:
          GITHUB_TOKEN: ${{ secrets.DOCUMENTATION_PAT_TOKEN }}
        run: |
          cd docs
          make clean
          make compile

          make html
          python3 wrap_run_llm.py
          cd _build/html

          git clone https://$<EMAIL>/sgl-project/sgl-project.github.io.git ../sgl-project.github.io --depth 1
          find ../sgl-project.github.io/ -mindepth 1 -not -path "../sgl-project.github.io/.git*" -not -name CNAME -not -name ".jekyll" -not -name ".nojekyll" -delete
          cp -r * ../sgl-project.github.io
          cp ../../README.md ../sgl-project.github.io/README.md
          cd ../sgl-project.github.io
          git config user.name "zhaochenyang20"
          git config user.email "<EMAIL>"
          git add .
          git commit -m "Update $(date +'%Y-%m-%d %H:%M:%S')"
          git push https://$<EMAIL>/sgl-project/sgl-project.github.io.git main
          cd ..
          rm -rf sgl-project.github.io
