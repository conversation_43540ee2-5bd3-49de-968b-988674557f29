name: VLLM Dependency Test

on:
  push:
    branches: [ main ]
    paths:
      - "python/**"
      - "scripts/**"
      - "test/**"
  pull_request:
    branches: [ main ]
    paths:
      - "python/**"
      - "scripts/**"
      - "test/**"

concurrency:
  group: vllm-dependency-test-${{ github.ref }}
  cancel-in-progress: true

jobs:
  vllm-dependency-test:
    if: (github.repository == 'sgl-project/sglang' || github.event_name == 'pull_request') &&
        github.event.pull_request.draft == false
    runs-on: 1-gpu-runner
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          bash scripts/ci_install_dependency.sh
          pip install "vllm==0.8.4"
          pip install "bitsandbytes>=0.44.0"

      - name: Run VLLM dependency tests
        timeout-minutes: 60
        run: |
          cd test/srt
          python3 run_suite.py --suite vllm_dependency_test --timeout-per-file 3600
