name: Experiment Runner

on:
  workflow_dispatch:
    inputs:
      script:
        description: "Experiment Runner Script"
        default: "configs/sharegpt_config.yaml"

concurrency:
  group: experiment-runner-${{ github.ref }}
  cancel-in-progress: true

jobs:
  experiment-runner-1-gpu:
    if: github.repository == 'sgl-project/sglang' || github.event_name == 'pull_request'
    runs-on: 1-gpu-runner
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          bash scripts/ci_install_dependency.sh

      - name: Test experiment runner
        timeout-minutes: 120
        run: |
          cd test/srt
          python3 experiment_runner.py --config ${{ inputs.script }}
