name: Build DeepEP Docker Image

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * *'

jobs:
  build-dev:
    if: ${{ github.repository == 'sgl-project/sglang' }}
    runs-on: ubuntu-22.04

    strategy:
      matrix:
        variant:
          - base: lmsysorg/sglang:latest
            tag: deepep
          - base: lmsysorg/sglang:dev
            tag: dev-deepep
          - base: lmsysorg/sglang:blackwell
            tag: blackwell-deepep

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Free disk space
        uses: jlumbroso/free-disk-space@main
        with:
          tool-cache: false
          docker-images: false
          android: true
          dotnet: true
          haskell: true
          large-packages: true
          swap-storage: false

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and Push Docker Image
        run: |
          docker build . -f docker/Dockerfile.deepep --build-arg BASE_IMAGE=${{ matrix.variant.base }} -t lmsysorg/sglang:${{ matrix.variant.tag }} --no-cache
          docker push lmsysorg/sglang:${{ matrix.variant.tag }}
