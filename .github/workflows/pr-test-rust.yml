name: PR Test (Rust)

on:
  push:
    branches: [ main ]
    paths:
      - "sgl-router/**"
  pull_request:
    branches: [ main ]
    paths:
      - "sgl-router/**"
  workflow_dispatch:

concurrency:
  group: pr-test-rust-${{ github.ref }}
  cancel-in-progress: true

jobs:
  unit-test-rust:
    if: github.repository == 'sgl-project/sglang' || github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          bash scripts/ci_install_rust.sh

      - name: Run fmt
        run: |
          source "$HOME/.cargo/env"
          cd sgl-router/
          cargo fmt -- --check

      - name: Run test
        timeout-minutes: 20
        run: |
          source "$HOME/.cargo/env"
          cd sgl-router/
          cargo test

  e2e-python:
    if: github.repository == 'sgl-project/sglang' || github.event_name == 'pull_request'
    runs-on: 2-gpu-runner
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install rust dependencies
        run: |
          bash scripts/ci_install_rust.sh

      - name: Build python binding
        run: |
          source "$HOME/.cargo/env"
          cd sgl-router
          pip install setuptools-rust wheel build
          python3 -m build
          pip install --force-reinstall dist/*.whl
      - name: Run e2e test
        run: |
          bash scripts/killall_sglang.sh "nuk_gpus"
          cd sgl-router/py_test
          python3 run_suite.py

  finish:
    needs: [unit-test-rust, e2e-python]
    runs-on: ubuntu-latest
    steps:
      - name: Finish
        run: echo "This is an empty step to ensure that all jobs are completed."
