name: Build Development Docker Image

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * *'

jobs:
  build-dev:
    if: ${{ github.repository == 'sgl-project/sglang' }}
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Free disk space
        uses: jlumbroso/free-disk-space@main
        with:
          tool-cache: false
          docker-images: false
          android: true
          dotnet: true
          haskell: true
          large-packages: true
          swap-storage: false

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and Push Dev Image
        run: |
          docker build . -f docker/Dockerfile.dev -t lmsysorg/sglang:dev --no-cache
          docker push lmsysorg/sglang:dev
