name: Execute Notebooks

on:
  pull_request:
    branches: [ main ]
    paths:
      - "python/sglang/**"
      - "docs/**"
  workflow_dispatch:


concurrency:
  group: execute-notebook-${{ github.ref }}
  cancel-in-progress: true


jobs:
  run-all-notebooks:
    runs-on: 1-gpu-runner
    if: github.repository == 'sgl-project/sglang' || github.event_name == 'pull_request'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          bash scripts/ci_install_dependency.sh
          pip install -r docs/requirements.txt
          apt-get update
          apt-get install -y pandoc
          apt-get update && apt-get install -y parallel retry

          ln -sf "$(which python3)" /usr/bin/python

      - name: Setup Jupyter Kernel
        run: |
          python -m ipykernel install --user --name python3 --display-name "Python 3"

      - name: Execute notebooks
        timeout-minutes: 40
        run: |
          cd docs
          make clean
          make compile


  finish:
    needs: [
      run-all-notebooks
    ]
    runs-on: ubuntu-latest
    steps:
      - name: Check all dependent job statuses
        run: |
          results=(${{ join(needs.*.result, ' ') }})
          for result in "${results[@]}"; do
            if [ "$result" = "failure" ] || [ "$result" = "cancelled" ]; then
              echo "Job failed with result: $result"
              exit 1
            fi
          done
          echo "All jobs completed successfully"
          exit 0
