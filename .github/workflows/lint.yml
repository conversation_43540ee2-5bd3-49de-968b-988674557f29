name: Lint

on: [ pull_request ]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      - name: Install pre-commit hook
        run: |
          python -m pip install pre-commit
          pre-commit install

      - name: Linting
        run: pre-commit run --all-files --show-diff-on-failure
