name: Release Fake Tag
on:
  push:
    branches:
      - main
    paths:
      - "python/sglang/version.py"
  workflow_dispatch:

permissions:
  contents: write

jobs:
  publish:
    if: github.repository == 'sgl-project/sglang'
    runs-on: ubuntu-latest
    environment: 'prod'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Get version
        id: get_version
        run: |
          version=$(cat python/sglang/version.py | cut -d'"' -f2)
          echo "TAG=v$version" >> $GITHUB_OUTPUT

      - name: Create and push fake tag
        env:
          GITHUB_TOKEN: ${{ secrets.REPO_TOKEN }}
        run: |
          git config user.name zhyncs
          git config user.email <EMAIL>
          git checkout -b ${{ steps.get_version.outputs.TAG }}
          git push --set-upstream origin ${{ steps.get_version.outputs.TAG }}
