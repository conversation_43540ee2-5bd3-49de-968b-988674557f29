FROM ubuntu:24.04
SHELL ["/bin/bash", "-c"]

ARG VER_SGLANG=main
ARG VER_TORCH=2.6.0
ARG VER_TORCHVISION=0.21.0

RUN apt-get update && \
    apt-get full-upgrade -y && \
    DEBIAN_FRONTEND=noninteractive apt-get install --no-install-recommends -y \
    ca-certificates \
    git \
    curl \
    wget \
    vim \
    gcc \
    g++ \
    make

WORKDIR /sgl-workspace

RUN curl -fsSL -v -o miniforge.sh -O https://github.com/conda-forge/miniforge/releases/download/24.11.3-2/Miniforge3-24.11.3-2-Linux-x86_64.sh && \
    bash miniforge.sh -b -p ./miniforge3 && \
    rm -f miniforge.sh && \
    . miniforge3/bin/activate && \
    conda install -y libsqlite==3.48.0 gperftools tbb libnuma numactl

ENV PATH=/sgl-workspace/miniforge3/bin:/sgl-workspace/miniforge3/condabin:${PATH}
ENV PIP_ROOT_USER_ACTION=ignore

RUN pip install intel-openmp

RUN git clone https://github.com/sgl-project/sglang.git && \
    cd sglang && \
    git checkout ${VER_SGLANG} && \
    pip install -e "python[all_cpu]" && \
    pip install torch==${VER_TORCH} torchvision==${VER_TORCHVISION} --index-url https://download.pytorch.org/whl/cpu --force-reinstall && \
    cd sgl-kernel && \
    cp pyproject_cpu.toml pyproject.toml && \
    pip install -v .

ENV LD_PRELOAD=/sgl-workspace/miniforge3/lib/libiomp5.so:/sgl-workspace/miniforge3/lib/libtcmalloc.so:/sgl-workspace/miniforge3/lib/libtbbmalloc.so.2

WORKDIR /sgl-workspace/sglang
